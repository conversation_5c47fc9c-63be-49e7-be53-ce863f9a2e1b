const TicketService = require("../services/ticketService");
const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

class TicketController {
  constructor() {
    this.ticketService = TicketService.getInstance();
  }

  /**
   * Helper method to get user ID from account ID
   */
  async getUserId(userInfo) {
    try {
      const { accountId, roleType } = userInfo;

      if (roleType !== "user") {
        throw new Error("Only users can purchase tickets");
      }

      const user = await prisma.users.findUnique({
        where: { account_id: accountId },
        select: { user_id: true },
      });

      if (!user) {
        throw new Error("User not found");
      }

      return user.user_id;
    } catch (error) {
      console.error("Error getting user ID:", error);
      throw error;
    }
  }

  /**
   * Create tickets with complete workflow
   * POST /api/tickets/create
   */
  createTickets = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { selectedTickets, ticketsWithAttendeeInfo, eventId } = req.body;

      // Validate user role
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can purchase tickets.",
        });
      }

      // Validate required fields
      if (!selectedTickets || !ticketsWithAttendeeInfo || !eventId) {
        return res.status(400).json({
          success: false,
          message:
            "Missing required fields: selectedTickets, ticketsWithAttendeeInfo, eventId",
        });
      }

      if (!Array.isArray(selectedTickets) || selectedTickets.length === 0) {
        return res.status(400).json({
          success: false,
          message: "selectedTickets must be a non-empty array",
        });
      }

      if (
        !Array.isArray(ticketsWithAttendeeInfo) ||
        ticketsWithAttendeeInfo.length === 0
      ) {
        return res.status(400).json({
          success: false,
          message: "ticketsWithAttendeeInfo must be a non-empty array",
        });
      }

      // Get user ID
      const userId = await this.getUserId(req.user);

      // Validate ticket availability
      for (const ticket of selectedTickets) {
        const ticketType = await prisma.tickettypes.findUnique({
          where: { ticket_type_id: ticket.ticketTypeId },
        });

        if (!ticketType) {
          return res.status(404).json({
            success: false,
            message: `Ticket type ${ticket.ticketTypeId} not found`,
          });
        }

        if (ticketType.quantity_available < ticket.quantity) {
          return res.status(400).json({
            success: false,
            message: `Not enough tickets available for ${ticketType.name}. Available: ${ticketType.quantity_available}, Requested: ${ticket.quantity}`,
          });
        }
      }

      // Step 1: Create order and order items
      const orderData = await this.ticketService.createOrderWithItems(
        userId,
        selectedTickets,
        eventId
      );

      // Step 2: Create tickets with complete workflow (QR codes, PDFs, storage)
      const tickets = await this.ticketService.createCompleteTickets(
        orderData,
        ticketsWithAttendeeInfo
      );

      // Step 3: Update ticket quantities
      for (const ticket of selectedTickets) {
        await prisma.tickettypes.update({
          where: { ticket_type_id: ticket.ticketTypeId },
          data: {
            quantity_available: {
              decrement: ticket.quantity,
            },
          },
        });
      }

      res.status(201).json({
        success: true,
        message: "Tickets created successfully",
        data: {
          orderId: orderData.order_id,
          tickets: tickets.map((ticket) => ({
            ticketId: ticket.ticket_id,
            qrCode: ticket.qr_code,
            attendeeName: ticket.attendee_name,
            attendeeEmail: ticket.attendee_email,
            attendeePhone: ticket.attendee_phone,
            ticketType: ticket.tickettypes.name,
            eventTitle: ticket.tickettypes.events.title,
            pdfAvailable: !!ticket.user_ticketpdf,
          })),
        },
      });
    } catch (error) {
      console.error("Error creating tickets:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create tickets",
        data: null,
      });
    }
  };

  /**
   * Download ticket PDF
   * GET /api/tickets/:ticketId/pdf
   */
  downloadTicketPDF = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { ticketId } = req.params;

      // Validate user role
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can download tickets.",
        });
      }

      // Validate ticket ID
      const ticketIdNum = parseInt(ticketId);
      if (isNaN(ticketIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid ticket ID",
        });
      }

      // Get user ID
      const userId = await this.getUserId(req.user);

      // Get PDF from service
      const pdfData = await this.ticketService.getTicketPDF(
        ticketIdNum,
        userId
      );

      // Set response headers for PDF download
      res.setHeader("Content-Type", pdfData.contentType);
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${pdfData.filename}"`
      );
      res.setHeader("Content-Length", pdfData.data.size);

      // Convert blob to buffer and send
      const buffer = Buffer.from(await pdfData.data.arrayBuffer());
      res.send(buffer);
    } catch (error) {
      console.error("Error downloading ticket PDF:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to download ticket PDF",
        data: null,
      });
    }
  };

  /**
   * Get ticket details by QR code (for validation/scanning)
   * GET /api/tickets/qr/:qrCode
   */
  getTicketByQRCode = async (req, res) => {
    try {
      const { qrCode } = req.params;

      if (!qrCode) {
        return res.status(400).json({
          success: false,
          message: "QR code is required",
        });
      }

      // Find ticket by QR code
      const ticket = await prisma.tickets.findUnique({
        where: { qr_code: decodeURIComponent(qrCode) },
        include: {
          tickettypes: {
            include: {
              events: {
                include: {
                  locations: true,
                },
              },
            },
          },
          orders: {
            include: {
              users: {
                select: {
                  first_name: true,
                  last_name: true,
                },
              },
            },
          },
        },
      });

      if (!ticket) {
        return res.status(404).json({
          success: false,
          message: "Ticket not found",
        });
      }

      const event = ticket.tickettypes.events;
      const location = event.locations;

      res.status(200).json({
        success: true,
        message: "Ticket found",
        data: {
          ticketId: ticket.ticket_id,
          qrCode: ticket.qr_code,
          isValidated: ticket.is_validated,
          validationTime: ticket.validation_time,
          attendeeName: ticket.attendee_name,
          attendeeEmail: ticket.attendee_email,
          attendeePhone: ticket.attendee_phone,
          ticketType: ticket.tickettypes.name,
          event: {
            id: event.event_id,
            title: event.title,
            startDate: event.start_date,
            startTime: event.start_time,
            venue: location?.venue_name || "TBD",
            city: location?.city || "TBD",
          },
          purchaser: {
            name: `${ticket.orders.users.first_name} ${ticket.orders.users.last_name}`,
          },
        },
      });
    } catch (error) {
      console.error("Error getting ticket by QR code:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get ticket details",
        data: null,
      });
    }
  };

  /**
   * Validate/scan a ticket
   * POST /api/tickets/:ticketId/validate
   */
  validateTicket = async (req, res) => {
    try {
      const { ticketId } = req.params;
      const { organizerId } = req.body; // For organizer validation

      // Validate ticket ID
      const ticketIdNum = parseInt(ticketId);
      if (isNaN(ticketIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid ticket ID",
        });
      }

      // Find and validate ticket
      const ticket = await prisma.tickets.findUnique({
        where: { ticket_id: ticketIdNum },
        include: {
          tickettypes: {
            include: {
              events: true,
            },
          },
        },
      });

      if (!ticket) {
        return res.status(404).json({
          success: false,
          message: "Ticket not found",
        });
      }

      if (ticket.is_validated) {
        return res.status(400).json({
          success: false,
          message: "Ticket has already been validated",
          data: {
            validationTime: ticket.validation_time,
          },
        });
      }

      // Update ticket as validated
      const updatedTicket = await prisma.tickets.update({
        where: { ticket_id: ticketIdNum },
        data: {
          is_validated: true,
          validation_time: new Date(),
        },
      });

      res.status(200).json({
        success: true,
        message: "Ticket validated successfully",
        data: {
          ticketId: updatedTicket.ticket_id,
          validationTime: updatedTicket.validation_time,
          attendeeName: updatedTicket.attendee_name,
        },
      });
    } catch (error) {
      console.error("Error validating ticket:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to validate ticket",
        data: null,
      });
    }
  };

  // Generate tickets from existing order (for two-stage purchase flow)
  generateTicketsFromOrder = async (req, res) => {
    try {
      const { orderId } = req.params;
      const { ticketsWithAttendeeInfo } = req.body;

      // Validate orderId
      const orderIdNum = parseInt(orderId);
      if (isNaN(orderIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid order ID",
        });
      }

      if (!ticketsWithAttendeeInfo || !Array.isArray(ticketsWithAttendeeInfo)) {
        return res.status(400).json({
          success: false,
          message: "Attendee information is required",
        });
      }

      // Get user ID
      const userId = await this.getUserId(req.user);

      // Verify order belongs to user
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderIdNum,
          user_id: userId,
        },
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: "Order not found",
        });
      }

      // Generate tickets
      const result = await this.ticketService.generateTicketsFromOrder(
        orderIdNum,
        ticketsWithAttendeeInfo
      );

      res.status(201).json({
        success: true,
        message: "Tickets generated successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error generating tickets from order:", error);

      if (
        error.message.includes("not found") ||
        error.message.includes("not completed") ||
        error.message.includes("already generated")
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to generate tickets",
        data: null,
      });
    }
  };
}

module.exports = TicketController;
