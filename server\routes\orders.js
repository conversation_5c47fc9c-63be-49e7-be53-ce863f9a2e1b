const express = require("express");
const OrderController = require("../controllers/orderController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const orderController = new OrderController();

// All order routes require authentication
router.use(verifyTokenFromCookie);

// Get user's order statistics
// GET /api/orders/stats
router.get("/stats", orderController.getUserOrderStats);

// Get user's tickets (formatted for dashboard)
// GET /api/orders/tickets
router.get("/tickets", orderController.getUserTickets);

// Get user's pending orders
// GET /api/orders/pending
router.get("/pending", orderController.getUserPendingOrders);

// Cart-like functionality using pending orders
// Get cart items (pending order items)
// GET /api/orders/cart
router.get("/cart", orderController.getCartItems);

// Get cart summary (total items, total price)
// GET /api/orders/cart/summary
router.get("/cart/summary", orderController.getCartSummary);

// Add item to cart (create pending order item)
// POST /api/orders/cart
router.post("/cart", orderController.addToCart);

// Update cart item quantity (update pending order item)
// PUT /api/orders/cart/:cartId
router.put("/cart/:cartId", orderController.updateCartItemQuantity);

// Remove item from cart (remove pending order item)
// DELETE /api/orders/cart/:cartId
router.delete("/cart/:cartId", orderController.removeFromCart);

// Clear entire cart (delete all pending orders)
// DELETE /api/orders/cart
router.delete("/cart", orderController.clearCart);

// Create order from cart items
// POST /api/orders/from-cart
router.post("/from-cart", orderController.createOrderFromCart);

// Create order from selected tickets (direct purchase)
// POST /api/orders/from-tickets
router.post("/from-tickets", orderController.createOrderFromTickets);

// Update order payment status
// PUT /api/orders/:orderId/payment-status
router.put(
  "/:orderId/payment-status",
  orderController.updateOrderPaymentStatus
);

// Get user's orders
// GET /api/orders
router.get("/", orderController.getUserOrders);

// Get specific order details
// GET /api/orders/:orderId
router.get("/:orderId", orderController.getOrderById);

module.exports = router;
