const { payment_status_type } = require("@prisma/client");
const SSLService = require("../services/sslService");

class SSLController {
  constructor() {
    this.sslService = SSLService.getInstance();
  }

  initiatePayment = async (req, res) => {
    try {
      const paymentData = await prisma.orders.findUnique({
        where: {
          order_id: req.body.orderId,
          payment_status: "pending",
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: true,
                },
              },
            },
          },
          tickets: true,
        },
      });
      if (!paymentData) {
        throw new Error("Order not found or already paid");
      }
      const response = await this.sslService.initiatePayment(paymentData);
      res.json(response);
    } catch (error) {
      console.error("Error initiating payment:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to initiate payment",
        data: null,
      });
    }
  };

  validatePayment = async (req, res) => {
    try {
      const validationData = req.body;
      const response = await this.sslService.validatePayment(validationData);
      res.json(response);
    } catch (error) {
      console.error("Error validating payment:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to validate payment",
        data: null,
      });
    }
  };
}

module.exports = SSLController;
