const express = require("express");
const SSLController = require("../controllers/sslController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const sslController = new SSLController();

router.use(verifyTokenFromCookie);

// Initiate payment
// POST /api/ssl/initiate
router.post("/initiate", sslController.initiatePayment);

// Validate payment
// POST /api/ssl/validate
router.post("/validate", sslController.validatePayment);

module.exports = router;
