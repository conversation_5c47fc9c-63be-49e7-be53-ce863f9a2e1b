# Changelog

## 2025-06-29

### Added

- **Interested Events Functionality**: Implemented complete interested events (wishlist) functionality with database persistence:
  - Created `InterestedService` class with Prisma integration for managing wishlist operations using the `wishlists` table
  - Created `InterestedController` class following MVC pattern for handling HTTP requests with proper authentication and error handling
  - Added interested routes (`/api/interested`) with endpoints for GET (get user's interested events), POST (add event to interested), and DELETE (remove event from interested)
  - Registered interested routes in main server.js file
- **Client-side Interested API**: Added `interestedAPI` functions to `client/lib/api.js` for making HTTP requests to the interested endpoints
- **Database-backed Interested Context**: Updated `interested-context.jsx` to use database API instead of localStorage:
  - Replaced localStorage persistence with database operations
  - Added loading states for better UX
  - Implemented proper error handling for API calls
  - Maintained backward compatibility with existing component usage

### Technical Improvements

- **Database Integration**: Enhanced interested functionality to use the existing `wishlists` table with proper foreign key relationships to `users` and `events` tables
- **Authentication**: All interested endpoints require proper user authentication and role validation
- **Error Handling**: Implemented comprehensive error handling with specific error messages for different scenarios (event not found, already in list, etc.)
- **API Consistency**: Followed existing API patterns and response structures for consistency across the application

## 2025-06-27

### Updated

- **OAuth Implementation**: Updated rules.md to replace the Supabase Auth UI codeblock with direct OAuth login implementation using `signInWithOAuth` method instead of `exchangeCodeForSession`. Created `client/lib/supabaseClient.js` with proper Supabase client setup using environment variables for client-side authentication.
- **Auth Context Integration**: Enhanced `client/context/auth-context.jsx` with Supabase session management, added `oAuthLogin` function for direct OAuth authentication, and integrated session state tracking with `onAuthStateChange`.
- **Auth Modal Update**: Modified `client/components/auth-modal.jsx` to use the new `oAuthLogin` function instead of server-side OAuth URL generation for Google authentication.
- **Auth Callback Enhancement**: Updated `client/app/auth/callback/page.jsx` to handle Supabase sessions directly using `getSession()` method for OAuth callback processing.

### Fixed

- **Login Error Display**: Fixed login error "Invalid email or password" not showing as toast notifications to users. The issue was caused by using two different toast systems - the auth context was using Sonner toasts but the layout was rendering a custom Toaster component. Replaced the custom Toaster with Sonner Toaster in layout.tsx to ensure login errors are properly displayed to users via toast notifications instead of server errors.

## 2025-06-28

### Added

- **Events API Backend**: Implemented complete events API backend with OOP architecture following MVC pattern:
  - Created `EventService` class with Prisma integration for database operations
  - Created `EventController` class for handling HTTP requests/responses
  - Added comprehensive event routes (`/api/events`) with filtering, pagination, and search capabilities
  - Implemented Singleton pattern for Prisma client management in `server/lib/prisma.js`
- **Events API Endpoints**: Added the following API endpoints:
  - `GET /api/events` - Get all events with optional filters (status, genre, location, search, pagination)
  - `GET /api/events/status/:status` - Get events by status (live, upcoming, past)
  - `GET /api/events/search` - Search events by query
  - `GET /api/events/genres` - Get all available genres
  - `GET /api/events/locations` - Get all available locations
  - `GET /api/events/organizer/:organizerId` - Get events by organizer
  - `GET /api/events/:id` - Get single event by ID
- **Frontend Event Management**: Implemented OOP event management system:
  - Created `EventManager` class using Singleton and Observer patterns for state management
  - Added `eventsAPI` service for API communication using Axios
  - Integrated real-time event loading with proper error handling and loading states
- **Events Page Integration**: Updated `/events` page to use real API data instead of mock data:
  - Replaced mock data imports with API calls
  - Added loading states with spinner animations
  - Implemented error handling with retry functionality
  - Updated filtering system to work with backend API
  - Added pagination controls for better UX
- **EventCard Component Update**: Modified EventCard component to handle new API data structure with proper field mappings for dates, times, locations, and pricing.

### Technical Improvements

- **Database Integration**: Enhanced server startup with proper Prisma connection management and graceful shutdown handling
- **Error Handling**: Implemented comprehensive error handling across all API endpoints with proper HTTP status codes
- **Code Architecture**: Applied OOP principles throughout the implementation with proper class structures and design patterns

### Updated

- **Event Status Filtering Logic**: Updated the filtering logic for live, upcoming, and past events according to business requirements:
  - **Live Events**: Events where current time is between `tickets_sale_start` and `tickets_sale_end` (ticket sales are active)
  - **Upcoming Events**: Events where current time is before `tickets_sale_start` (ticket sales haven't started yet)
  - **Past Events**: Events where current time is after `end_date` (event has completely finished)
- **Frontend-Based Filtering**: Completely refactored filtering system to be frontend-based for better performance:
  - **Single API Call**: Now fetches all events once instead of multiple API calls for each filter change
  - **Client-Side Processing**: All filtering (status, genre, location, search) now happens on the frontend
  - **Instant Filtering**: Filter changes are now instantaneous without network delays
  - **Better UX**: Significantly improved user experience with faster response times
- **EventFilter Class**: Created new OOP utility class for frontend filtering with methods for status, genre, location, and search filtering
- **Simplified Backend**: Removed server-side filtering endpoints and simplified API to single `/api/events` endpoint
- **Performance Optimization**: Reduced server load and improved app responsiveness by eliminating redundant API calls
- **Event Detail Page**: Completely refactored `/events/[id]` page to use real database data instead of mock data:
  - Replaced mock data imports with API calls using `eventsAPI.getEventById()`
  - Added proper loading states with spinner animations and error handling
  - Updated field mappings to match database schema (bannerImage, startDate, startTime, etc.)
  - Integrated real ticket types from database with proper availability tracking
  - Added support for event categories instead of hardcoded "days"
  - Updated artist display to use real artist data from database
  - Enhanced venue information with proper location data mapping
  - Fixed organizer information to use real organizer data
  - Updated cart integration to work with database ticket structure
  - Added proper date/time formatting for database timestamps

## 2025-06-29 - Implemented Tab Content for Ticket Types by Event Categories

- **Backend Updates**: Enhanced event service to include category_id in ticket types API responses
  - Updated `getEventById`, `getAllEvents`, `getEventsByOrganizer`, and `searchEvents` methods to include `category_id` in ticket types selection
  - Modified `formatEventResponse` to include `categoryId` field in ticket types mapping
- **Frontend Implementation**: Implemented TabsContent component to organize ticket types by their event categories
  - Added category-based tab navigation for ticket selection in event details page
  - Implemented filtering of ticket types by category_id within each tab
  - Added category descriptions display within each tab content
  - Maintained fallback display for events without categories (shows all tickets in single view)
  - Enhanced user experience by grouping related ticket types together logically
- **Hero Carousel Live Events Filter**: Updated hero carousel to only display live events instead of all events:
  - Modified home page to pass `liveEvents.slice(0, 5)` instead of `allEvents.slice(0, 5)` to HeroCarousel
  - Updated empty state message to specifically indicate "No Live Events" with helpful user guidance
  - Ensures hero carousel only showcases events with active ticket sales (live status)

## 2025-06-29

### Added

- **Cart Backend API**: Implemented complete cart functionality using backend database instead of localStorage:
  - Created `CartService` class with Prisma integration for cart operations (add, remove, update, clear)
  - Created `CartController` class following MVC pattern for handling cart HTTP requests
  - Added cart routes (`/api/cart`) with authentication middleware for all cart operations
  - Implemented cart validation including ticket availability, max per order limits, and user authentication
- **Cart API Endpoints**: Added the following authenticated cart endpoints:
  - `GET /api/cart` - Get user's cart items with full event and ticket details
  - `POST /api/cart` - Add item to cart with quantity validation
  - `PUT /api/cart/:cartId` - Update cart item quantity
  - `DELETE /api/cart/:cartId` - Remove specific item from cart
  - `DELETE /api/cart` - Clear entire cart
  - `GET /api/cart/summary` - Get cart summary (total items, total price)
- **Cart API Client Functions**: Added `cartAPI` service in `client/lib/api.js` for frontend cart operations

### Updated

- **Cart Context Refactor**: Completely refactored `client/context/cart-context.jsx` to use backend API:
  - Replaced localStorage-based cart with database-backed cart system
  - Added authentication integration - cart now syncs with user account
  - Implemented proper loading states and error handling with toast notifications
  - Added automatic cart loading when user logs in and clearing when user logs out
  - Updated all cart operations (add, remove, update, clear) to use API calls
- **Event Page Cart Integration**: Updated `/events/[id]` page to work with new backend cart system:
  - Modified `handleAddToCart` function to pass `ticketTypeId` instead of event object
  - Added proper error handling and success feedback for cart operations
  - Implemented async cart operations with loading states
- **Cart Modal Enhancement**: Updated `client/components/cart-modal.jsx` for backend integration:
  - Modified cart item display to use new backend data structure (cart_id, eventTitle, etc.)
  - Updated quantity controls to use cart_id instead of array indices
  - Added loading states and disabled states during cart operations
  - Enhanced cart grouping to work with backend data structure
  - Added proper error handling and loading indicators

### Technical Improvements

- **Database-Backed Cart**: Cart items are now persisted in PostgreSQL database using the existing `cart` table
- **User Authentication**: All cart operations require user authentication via JWT cookies
- **Data Validation**: Added comprehensive validation for ticket availability, quantity limits, and user permissions
- **Error Handling**: Implemented proper error handling with user-friendly toast notifications
- **Performance**: Cart operations now provide real-time inventory checking and proper data consistency

### Fixed

- **Foreign Key Constraint Error**: Fixed `cart_user_id_fkey` foreign key constraint violation by properly mapping account_id to user_id:
  - Added `getUserIdFromAccountId()` helper method in CartService to resolve user_id from account_id
  - Updated all cart service methods to accept account_id and internally resolve to user_id
  - Modified cart controller to pass account_id from JWT token instead of trying to extract user_id
  - Ensures proper relationship between masteraccounts table (account_id) and users table (user_id) for cart operations

## 2025-07-01

### Added

- **Buy Now Functionality**: Implemented "Buy Now" button functionality for direct checkout of selected event tickets:

  - Added `handleBuyNow` function that adds selected tickets to cart and immediately redirects to checkout page
  - Created dual-button interface with separate "Buy Now" and "Add to Cart" buttons for authenticated users
  - Maintained existing "Add to Cart" functionality while adding express checkout option
  - Added proper error handling and success feedback for buy now operations
  - Implemented authentication check - shows "Sign in to Purchase" for unauthenticated users
  - Enhanced user experience by providing both cart-based and direct purchase workflows

- **Event-Specific Checkout**: Implemented event-specific checkout functionality for targeted ticket purchasing:
  - Added URL parameter support (`?eventId=X`) to checkout page for filtering specific event tickets
  - Modified cart modal "Checkout Event" button to redirect to event-specific checkout
  - Updated checkout page to filter cart items based on eventId parameter
  - Added visual indicator showing "(Event-specific checkout)" when filtering is active
  - Enhanced "Buy Now" functionality to redirect to event-specific checkout instead of general checkout
  - Implemented proper validation to redirect users if no items exist for the specified event
  - Maintained backward compatibility - checkout page works normally without eventId parameter

## 2025-07-02

### Added

- **Two-Stage Purchase Flow**: Implemented secure two-stage purchase process that separates order creation from ticket fulfillment:

  **Stage 1 - Order Creation (Ticket Info Modal)**:

  - Modified `ticket-info-modal.jsx` to create pending orders instead of immediately generating tickets
  - Added `ordersAPI.createOrderFromTickets()` for creating orders with pending payment status
  - Implemented session storage to pass order data between ticket selection and checkout
  - Added automatic redirection to checkout page after order creation
  - Enhanced user feedback with success messages and loading states

  **Stage 2 - Payment & Ticket Generation (Checkout Page)**:

  - Updated checkout page to handle both cart-based and direct order checkout modes
  - Added `ticketsAPI.generateTicketsFromOrder()` for creating tickets after payment confirmation
  - Implemented payment status updates from "pending" to "completed" with transaction IDs
  - Added ticket PDF generation and QR code creation only after payment confirmation
  - Enhanced checkout UI to support both purchase flows with proper loading states

- **Order Management Backend**: Created comprehensive order management system:

  - Added `createOrderFromCart()` and `createOrderFromTickets()` methods in OrderService
  - Added `updateOrderPaymentStatus()` for secure payment status transitions
  - Added `getUserPendingOrders()` for tracking incomplete purchases
  - Created order controller methods with proper authentication and validation
  - Added new API routes: `/api/orders/from-cart`, `/api/orders/from-tickets`, `/api/orders/pending`, `/api/orders/:orderId/payment-status`

- **Ticket Generation Service**: Separated ticket creation logic for post-payment processing:
  - Added `generateTicketsFromOrder()` method in TicketService for two-stage flow
  - Implemented order validation (payment status, duplicate prevention)
  - Added comprehensive ticket creation with QR codes, PDFs, and Supabase storage
  - Created `/api/tickets/generate/:orderId` endpoint for post-payment ticket generation
  - Enhanced error handling for order state validation and ticket generation

### Technical Improvements

- **Data Integrity**: Ensured tickets are only created after payment confirmation, preventing orphaned tickets
- **Payment Security**: Implemented proper transaction ID tracking and payment status management
- **Database Consistency**: Maintained proper relationships between cart, orders, and tickets tables
- **Error Handling**: Added comprehensive validation for order states, payment status, and attendee information
- **Session Management**: Used session storage for secure order data transfer between pages
- **API Architecture**: Extended existing MVC pattern with new order and ticket generation endpoints
