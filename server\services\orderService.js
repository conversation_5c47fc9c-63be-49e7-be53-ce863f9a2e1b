const { PrismaClient } = require("@prisma/client");
const { v4: uuidv4 } = require("uuid");
const prisma = new PrismaClient();

class OrderService {
  static instance = null;

  static getInstance() {
    if (!OrderService.instance) {
      OrderService.instance = new OrderService();
    }
    return OrderService.instance;
  }

  // Get all orders for a user with ticket details
  async getUserOrders(userId) {
    try {
      const orders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "completed", // Only show completed orders
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true,
                    },
                  },
                },
              },
            },
          },
          tickets: true,
        },
        orderBy: {
          created_at: "desc",
        },
      });

      return orders;
    } catch (error) {
      console.error("Error fetching user orders:", error);
      throw new Error("Failed to fetch user orders");
    }
  }

  // Get user tickets with event details (formatted for dashboard)
  async getUserTickets(userId) {
    console.log("Fetching user tickets for user ID:", userId);
    try {
      const tickets = await prisma.tickets.findMany({
        where: {
          orders: {
            user_id: userId,
            payment_status: "completed",
          },
        },
        include: {
          orders: true, // Include orders to access created_at
          tickettypes: {
            include: {
              events: {
                include: {
                  locations: true,
                  genres: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      // Format tickets for frontend
      const formattedTickets = tickets.map((ticket) => {
        const event = ticket.tickettypes.events;
        const location = event.locations;

        return {
          id: ticket.ticket_id,
          eventId: event.event_id,
          eventTitle: event.title,
          eventImage: event.banner_image,
          eventDate: event.start_date, // Fixed: use start_date from schema
          eventTime: event.start_time, // Fixed: use start_time from schema
          eventLocation: {
            venue: location?.venue_name || "TBD",
            city: location?.city || "TBD",
            address: location?.address || "TBD",
          },
          ticketType: ticket.tickettypes.name,
          price: parseFloat(ticket.tickettypes.price),
          purchaseDate: ticket.orders.created_at, // Now this will work with orders included
          qrCode: ticket.qr_code,
          isValidated: ticket.is_validated,
          validationTime: ticket.validation_time,
          ticketPdf: ticket.user_ticketpdf,
        };
      });
      console.log("Formatted tickets:", formattedTickets);
      return formattedTickets;
    } catch (error) {
      console.error("Error fetching user tickets:", error);
      throw new Error("Failed to fetch user tickets");
    }
  }

  // Get order by ID (for detailed view)
  async getOrderById(orderId, userId) {
    try {
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderId,
          user_id: userId,
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true,
                      genres: true,
                    },
                  },
                },
              },
            },
          },
          tickets: true,
          users: {
            select: {
              first_name: true,
              last_name: true,
              phone_number: true,
            },
          },
        },
      });

      if (!order) {
        throw new Error("Order not found");
      }

      return order;
    } catch (error) {
      console.error("Error fetching order:", error);
      throw new Error("Failed to fetch order details");
    }
  }

  // Get order statistics for user
  async getUserOrderStats(userId) {
    try {
      const stats = await prisma.orders.aggregate({
        where: {
          user_id: userId,
          payment_status: "completed",
        },
        _count: {
          order_id: true,
        },
        _sum: {
          total_amount: true,
        },
      });

      const ticketCount = await prisma.tickets.count({
        where: {
          orders: {
            user_id: userId,
            payment_status: "completed",
          },
        },
      });

      return {
        totalOrders: stats._count.order_id || 0,
        totalSpent: parseFloat(stats._sum.total_amount || 0),
        totalTickets: ticketCount,
      };
    } catch (error) {
      console.error("Error fetching user order stats:", error);
      throw new Error("Failed to fetch order statistics");
    }
  }

  /**
   * Create order from cart items with pending payment status
   */
  async createOrderFromCart(userId, cartItems) {
    try {
      // Calculate total amount from cart items
      const totalAmount = cartItems.reduce((sum, item) => {
        return sum + parseFloat(item.tickettypes.price) * item.quantity;
      }, 0);

      // Calculate additional fees
      const organizerFees = totalAmount * 0.05; // 5% organizer fee
      const serviceFees = totalAmount * 0.1; // 10% service fee
      const additionalFees = organizerFees + serviceFees;

      // Create order with pending status
      const order = await prisma.orders.create({
        data: {
          user_id: userId,
          total_amount: totalAmount,
          additional_fees: additionalFees,
          payment_status: "pending",
          payment_method: null,
          transaction_id: null,
        },
      });

      // Create order items from cart
      const orderItems = [];
      for (const cartItem of cartItems) {
        const orderItem = await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: cartItem.ticket_type_id,
            quantity: cartItem.quantity,
            unit_price: parseFloat(cartItem.tickettypes.price),
          },
        });
        orderItems.push(orderItem);
      }

      return {
        order,
        orderItems,
      };
    } catch (error) {
      console.error("Error creating order from cart:", error);
      throw error;
    }
  }

  /**
   * Create order from selected tickets with attendee info (for direct purchase)
   */
  async createOrderFromTickets(
    userId,
    selectedTickets,
    ticketsWithAttendeeInfo
  ) {
    try {
      // Calculate total amount
      const totalAmount = selectedTickets.reduce((sum, ticket) => {
        return sum + parseFloat(ticket.price) * ticket.quantity;
      }, 0);

      // Calculate additional fees
      const organizerFees = totalAmount * 0.05; // 5% organizer fee
      const serviceFees = totalAmount * 0.1; // 10% service fee
      const additionalFees = organizerFees + serviceFees;

      // Create order with pending status
      const order = await prisma.orders.create({
        data: {
          user_id: userId,
          total_amount: totalAmount,
          additional_fees: additionalFees,
          payment_status: "pending",
          payment_method: null,
          transaction_id: null,
        },
      });

      // Create order items
      const orderItems = [];
      for (const ticket of selectedTickets) {
        const orderItem = await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: ticket.ticketTypeId,
            quantity: ticket.quantity,
            unit_price: parseFloat(ticket.price),
          },
        });
        orderItems.push(orderItem);
      }

      return {
        order,
        orderItems,
        ticketsWithAttendeeInfo,
      };
    } catch (error) {
      console.error("Error creating order from tickets:", error);
      throw error;
    }
  }

  /**
   * Update order payment status and add transaction details
   */
  async updateOrderPaymentStatus(
    orderId,
    paymentStatus,
    transactionId,
    paymentMethod = "sslcommerz"
  ) {
    try {
      const updatedOrder = await prisma.orders.update({
        where: { order_id: orderId },
        data: {
          payment_status: paymentStatus,
          transaction_id: transactionId,
          payment_method: paymentMethod,
        },
      });

      return updatedOrder;
    } catch (error) {
      console.error("Error updating order payment status:", error);
      throw error;
    }
  }

  /**
   * Get pending orders for a user
   */
  async getUserPendingOrders(userId) {
    try {
      const orders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "pending",
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: true,
                  eventcategories: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      return orders;
    } catch (error) {
      console.error("Error getting user pending orders:", error);
      throw error;
    }
  }

  /**
   * Cancel pending order
   */
  async cancelPendingOrder(orderId) {
    try {
      const updatedOrder = await prisma.orders.update({
        where: {
          order_id: orderId,
          payment_status: "pending", // Only allow canceling pending orders
        },
        data: {
          payment_status: "failed",
        },
      });

      return updatedOrder;
    } catch (error) {
      console.error("Error canceling pending order:", error);
      throw error;
    }
  }

  /**
   * Get order total including fees
   */
  getOrderTotal(order) {
    return (
      parseFloat(order.total_amount) + parseFloat(order.additional_fees || 0)
    );
  }

  // ===== CART-LIKE FUNCTIONALITY USING PENDING ORDERS =====

  /**
   * Get cart items (pending order items) for a user
   */
  async getCartItems(userId) {
    try {
      const pendingOrders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "pending",
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    select: {
                      event_id: true,
                      title: true,
                      banner_image: true,
                      start_date: true,
                      end_date: true,
                      venue_name: true,
                    },
                  },
                  eventcategories: {
                    select: {
                      category_id: true,
                      name: true,
                      category_type: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      // Flatten order items to match cart structure
      const cartItems = [];
      for (const order of pendingOrders) {
        for (const orderItem of order.orderitems) {
          cartItems.push({
            cart_id: orderItem.order_item_id, // Use order_item_id as cart_id for compatibility
            order_id: order.order_id,
            user_id: userId,
            ticket_type_id: orderItem.ticket_type_id,
            quantity: orderItem.quantity,
            price: parseFloat(orderItem.unit_price),
            created_at: order.created_at,
            updated_at: order.created_at,
            tickettypes: orderItem.tickettypes,
          });
        }
      }

      return cartItems;
    } catch (error) {
      console.error("Error getting cart items from pending orders:", error);
      throw new Error("Failed to get cart items");
    }
  }

  /**
   * Add item to cart (create pending order item)
   */
  async addToCart(userId, ticketTypeId, quantity = 1) {
    try {
      // First, check if ticket type exists and has availability
      const ticketType = await prisma.tickettypes.findUnique({
        where: {
          ticket_type_id: ticketTypeId,
        },
      });

      if (!ticketType) {
        throw new Error("Ticket type not found");
      }

      if (ticketType.quantity_available < quantity) {
        throw new Error("Not enough tickets available");
      }

      // Check max per order limit
      if (ticketType.max_per_order && quantity > ticketType.max_per_order) {
        throw new Error(
          `Maximum ${ticketType.max_per_order} tickets allowed per order`
        );
      }

      // Check if there's already a pending order with this ticket type
      const existingOrder = await prisma.orders.findFirst({
        where: {
          user_id: userId,
          payment_status: "pending",
          orderitems: {
            some: {
              ticket_type_id: ticketTypeId,
            },
          },
        },
        include: {
          orderitems: {
            where: {
              ticket_type_id: ticketTypeId,
            },
          },
        },
      });

      if (existingOrder && existingOrder.orderitems.length > 0) {
        // Update existing order item quantity
        const existingOrderItem = existingOrder.orderitems[0];
        const newQuantity = existingOrderItem.quantity + quantity;

        // Check max per order limit for updated quantity
        if (
          ticketType.max_per_order &&
          newQuantity > ticketType.max_per_order
        ) {
          throw new Error(
            `Maximum ${ticketType.max_per_order} tickets allowed per order`
          );
        }

        // Update order item quantity
        const updatedOrderItem = await prisma.orderitems.update({
          where: {
            order_item_id: existingOrderItem.order_item_id,
          },
          data: {
            quantity: newQuantity,
          },
        });

        // Update order total
        const orderItems = await prisma.orderitems.findMany({
          where: { order_id: existingOrder.order_id },
          include: { tickettypes: true },
        });

        const totalAmount = orderItems.reduce((sum, item) => {
          return sum + parseFloat(item.tickettypes.price) * item.quantity;
        }, 0);

        await prisma.orders.update({
          where: { order_id: existingOrder.order_id },
          data: { total_amount: totalAmount },
        });

        return updatedOrderItem;
      } else {
        // Create new pending order with this item
        const totalAmount = parseFloat(ticketType.price) * quantity;

        const order = await prisma.orders.create({
          data: {
            user_id: userId,
            total_amount: totalAmount,
            additional_fees: 0.0,
            payment_status: "pending",
            payment_method: null,
            transaction_id: null,
          },
        });

        const orderItem = await prisma.orderitems.create({
          data: {
            order_id: order.order_id,
            ticket_type_id: ticketTypeId,
            quantity: quantity,
            unit_price: parseFloat(ticketType.price),
          },
        });

        return orderItem;
      }
    } catch (error) {
      console.error("Error adding to cart (pending order):", error);
      throw error;
    }
  }

  /**
   * Update cart item quantity (update pending order item)
   */
  async updateCartItemQuantity(userId, cartId, quantity) {
    try {
      // cartId is actually order_item_id in our new system
      const orderItem = await prisma.orderitems.findFirst({
        where: {
          order_item_id: cartId,
          orders: {
            user_id: userId,
            payment_status: "pending",
          },
        },
        include: {
          tickettypes: true,
          orders: true,
        },
      });

      if (!orderItem) {
        throw new Error("Cart item not found");
      }

      // Validate quantity
      if (quantity <= 0) {
        throw new Error("Quantity must be greater than 0");
      }

      // Check max per order limit
      if (
        orderItem.tickettypes.max_per_order &&
        quantity > orderItem.tickettypes.max_per_order
      ) {
        throw new Error(
          `Maximum ${orderItem.tickettypes.max_per_order} tickets allowed per order`
        );
      }

      // Check availability
      if (orderItem.tickettypes.quantity_available < quantity) {
        throw new Error("Not enough tickets available");
      }

      // Update order item quantity
      const updatedOrderItem = await prisma.orderitems.update({
        where: {
          order_item_id: cartId,
        },
        data: {
          quantity: quantity,
        },
      });

      // Update order total
      const orderItems = await prisma.orderitems.findMany({
        where: { order_id: orderItem.order_id },
        include: { tickettypes: true },
      });

      const totalAmount = orderItems.reduce((sum, item) => {
        return sum + parseFloat(item.tickettypes.price) * item.quantity;
      }, 0);

      await prisma.orders.update({
        where: { order_id: orderItem.order_id },
        data: { total_amount: totalAmount },
      });

      return updatedOrderItem;
    } catch (error) {
      console.error("Error updating cart item quantity:", error);
      throw error;
    }
  }

  /**
   * Remove item from cart (remove pending order item)
   */
  async removeFromCart(userId, cartId) {
    try {
      // cartId is actually order_item_id in our new system
      const orderItem = await prisma.orderitems.findFirst({
        where: {
          order_item_id: cartId,
          orders: {
            user_id: userId,
            payment_status: "pending",
          },
        },
        include: {
          orders: true,
        },
      });

      if (!orderItem) {
        throw new Error("Cart item not found");
      }

      const orderId = orderItem.order_id;

      // Delete the order item
      await prisma.orderitems.delete({
        where: {
          order_item_id: cartId,
        },
      });

      // Check if this was the last item in the order
      const remainingItems = await prisma.orderitems.findMany({
        where: { order_id: orderId },
      });

      if (remainingItems.length === 0) {
        // Delete the empty order
        await prisma.orders.delete({
          where: { order_id: orderId },
        });
      } else {
        // Update order total
        const orderItems = await prisma.orderitems.findMany({
          where: { order_id: orderId },
          include: { tickettypes: true },
        });

        const totalAmount = orderItems.reduce((sum, item) => {
          return sum + parseFloat(item.tickettypes.price) * item.quantity;
        }, 0);

        await prisma.orders.update({
          where: { order_id: orderId },
          data: { total_amount: totalAmount },
        });
      }

      return { success: true };
    } catch (error) {
      console.error("Error removing from cart:", error);
      throw error;
    }
  }

  /**
   * Clear entire cart (delete all pending orders for user)
   */
  async clearCart(userId) {
    try {
      // Delete all pending orders and their items for the user
      await prisma.orders.deleteMany({
        where: {
          user_id: userId,
          payment_status: "pending",
        },
      });

      return { success: true };
    } catch (error) {
      console.error("Error clearing cart:", error);
      throw error;
    }
  }

  /**
   * Get cart summary (total items, total price)
   */
  async getCartSummary(userId) {
    try {
      const pendingOrders = await prisma.orders.findMany({
        where: {
          user_id: userId,
          payment_status: "pending",
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                select: {
                  price: true,
                },
              },
            },
          },
        },
      });

      let totalItems = 0;
      let totalPrice = 0;
      let itemCount = 0;

      for (const order of pendingOrders) {
        for (const orderItem of order.orderitems) {
          totalItems += orderItem.quantity;
          totalPrice +=
            parseFloat(orderItem.tickettypes.price) * orderItem.quantity;
          itemCount++;
        }
      }

      return {
        totalItems,
        totalPrice: parseFloat(totalPrice.toFixed(2)),
        itemCount,
      };
    } catch (error) {
      console.error("Error getting cart summary:", error);
      throw new Error("Failed to get cart summary");
    }
  }
}

module.exports = OrderService;
