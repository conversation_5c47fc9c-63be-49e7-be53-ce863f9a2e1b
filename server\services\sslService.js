const { PrismaClient } = require("@prisma/client");

const SSLCommerzPayment = require("sslcommerz-lts");
const store_id = process.env.SSL_STORE_ID;
const store_passwd = process.env.SSL_STORE_PASS;
const is_live = false; //true for live, false for sandbox

class SSLService {
  static instance = null;

  static getInstance() {
    if (!SSLService.instance) {
      SSLService.instance = new SSLService();
    }
    return SSLService.instance;
  }

  async initiatePayment(paymentData) {
    // using payment data to fill the data object
    const data = {
      total_amount: 100,
      currency: "BDT",
      tran_id: "REF123", // transaction id from orders table
      success_url: "http://localhost:3030/success",
      fail_url: "http://localhost:3030/fail",
      cancel_url: "http://localhost:3030/cancel",
      ipn_url: "http://localhost:3030/ipn",

      payment_method: "", // payment method

      event_name: "", //event name
      event_start_date: "", //event start date

      ticket_category: "", //ticket category
      ticket_type: "", //ticket type

      cus_name: "", // user name
      cus_email: "", // user email

      cus_country: "Bangladesh",
      cus_phone: "01711111111",
    };

    const sslcz = new SSLCommerzPayment(store_id, store_passwd, is_live);
    sslcz.init(data).then((apiResponse) => {
      // Redirect the user to payment gateway
      let GatewayPageURL = apiResponse.GatewayPageURL;
      res.send({ url: GatewayPageURL });
      console.log("sending to client: ", GatewayPageURL);
    });
  }

  async validatePayment(validationData) {
    try {
      const sslcz = new SSLCommerzPayment(store_id, store_passwd, is_live);
      const response = await sslcz.validate(validationData);
      return response;
    } catch (error) {
      console.error("Error validating payment:", error);
      throw error;
    }
  }

  async paymentSuccess(paymentData) {
    try {
      const sslcz = new SSLCommerzPayment(store_id, store_passwd, is_live);
      const response = await sslcz.paymentSuccess(paymentData);
      return response;
    } catch (error) {
      console.error("Error processing payment success:", error);
      throw error;
    }
  }
}

module.exports = SSLService;
